#include "WifiHandler.h"

#include "config.h"

// Static instance for event handling
WifiHandler* WifiHandler_instance = nullptr;

WifiHandler::WifiHandler() {
    ssid = nullptr;
    password = nullptr;
    isConnected = false;
    lastConnectionAttempt = 0;
    WifiHandler_instance = this;
}

WifiHandler::~WifiHandler() {
    disconnect();
    WifiHandler_instance = nullptr;
}

void WifiHandler::setup(const char* ssid, const char* password) {
    this->ssid = ssid;
    this->password = password;
    
    Serial.println("WiFi Handler: Initializing...");
    
    // Set WiFi mode to station
    WiFi.mode(WIFI_STA);
    
    // Register event handler
    WiFi.onEvent(WiFiEventHandler);
    
    // Attempt initial connection
    connect();
}

void WifiHandler::loop() {
    // Check if we need to retry connection
    if (!isConnected && 
        (millis() - lastConnectionAttempt > RETRY_INTERVAL)) {
        Serial.println("WiFi Handler: Attempting reconnection...");
        connect();
    }
}

bool WifiHandler::connect() {
    if (ssid == nullptr || password == nullptr) {
        Serial.println("WiFi Handler: SSID or password not set");
        return false;
    }
    
    Serial.print("WiFi Handler: Connecting to ");
    Serial.println(ssid);
    
    lastConnectionAttempt = millis();
    WiFi.begin(ssid, password);
    
    // Wait for connection with timeout
    unsigned long startTime = millis();
    while (WiFi.status() != WL_CONNECTED && 
           (millis() - startTime) < CONNECTION_TIMEOUT) {
        delay(500);
        Serial.print(".");
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        isConnected = true;
        Serial.println();
        printConnectionInfo();
        return true;
    } else {
        isConnected = false;
        Serial.println();
        Serial.println("WiFi Handler: Connection failed");
        return false;
    }
}

void WifiHandler::disconnect() {
    if (isConnected) {
        WiFi.disconnect();
        isConnected = false;
        Serial.println("WiFi Handler: Disconnected");
    }
}

bool WifiHandler::isWifiConnected() {
    return isConnected && (WiFi.status() == WL_CONNECTED);
}

String WifiHandler::getLocalIP() {
    if (isWifiConnected()) {
        return WiFi.localIP().toString();
    }
    return "Not connected";
}

int WifiHandler::getSignalStrength() {
    if (isWifiConnected()) {
        return WiFi.RSSI();
    }
    return 0;
}

void WifiHandler::printConnectionInfo() {
    Serial.println("WiFi Handler: Connected successfully!");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
    Serial.print("Signal strength: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
    Serial.print("MAC address: ");
    Serial.println(WiFi.macAddress());

    config_set
}

void WifiHandler::WiFiEventHandler(WiFiEvent_t event) {
    if (WifiHandler_instance != nullptr) {
        WifiHandler_instance->onWiFiEvent(event);
    }
}

void WifiHandler::onWiFiEvent(WiFiEvent_t event) {
    switch (event) {
        case ARDUINO_EVENT_WIFI_STA_CONNECTED:
            Serial.println("WiFi Handler: Connected to AP");
            break;
            
        case ARDUINO_EVENT_WIFI_STA_GOT_IP:
            isConnected = true;
            Serial.println("WiFi Handler: Got IP address");
            printConnectionInfo();
            break;
            
        case ARDUINO_EVENT_WIFI_STA_DISCONNECTED:
            isConnected = false;
            Serial.println("WiFi Handler: Disconnected from AP");
            break;
            
        case ARDUINO_EVENT_WIFI_STA_LOST_IP:
            isConnected = false;
            Serial.println("WiFi Handler: Lost IP address");
            break;
            
        default:
            break;
    }
}

#include <Arduino.h>
#include <LovyanGFX.hpp>
#include <WiFi.h>
#include <OneWire.h>
#include <DallasTemperature.h>
#include <WiFiClient.h>
#include <PubSubClient.h>

#include "lilygo-t-display-s3.h"
#include "WifiHandler.h"
#include "config.h"

#define ONE_WIRE_BUS 2
OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature sensors(&oneWire);

WiFiClient wifiClient;
PubSubClient mqttClient(wifiClient);

WifiHandler *wifi;
LGFX _gfx;

unsigned long lastDataPublish = 0;
unsigned long lastTempData = 0;
unsigned long lastTrigger = 0;
float temperatureC = 0;

void reconnect()
{
  while (!mqttClient.connected()) {
    Serial.print("Attempting MQTT connection...");
    if (mqttClient.connect("ESP32Client")) {
      Serial.println("connected");
    } else {
      Serial.print("failed, rc=");
      Serial.print(mqttClient.state());
      Serial.println(" try again in 5 seconds");
      delay(5000);
    }
  }

  String cfgTopic = "C" + String(config_get()->mac);
  if (mqttClient.subscribe(cfgTopic.c_str())) {
    Serial.printf("Subscribed to config topic %s\n", cfgTopic.c_str());
  }
}

bool mqtt_publish_config()
{
  const char *config = config_get_string();

  Serial.print("Sending config: ");
  Serial.println(config);

  return mqttClient.publish("txdev/cr/c1", config);
}

bool mqtt_publish_data(const float temp)
{
	char data[128];
  const Config *cfg = config_get();

	snprintf(data, sizeof(data),"%s:%.01f:%.01f:%.01f:%d:%d:%s:%d:%s:%s:%d:", 
    cfg->mac,
    cfg->tempMax,
    temp,
    cfg->tempMin,
    0,
    0,
		"0.0",
    0,
    "0.0",
		"",
		WiFi.RSSI()
	);

  Serial.print("Sending data: ");
  Serial.println(data);

  return mqttClient.publish("txdev/cr/d1", data);
};

bool mqtt_publish_trigger(const float temp)
{
	char data[128];
  const Config *cfg = config_get();

	snprintf(data, sizeof(data),"%s:%.01f:%s:%s:%.01f:%.01f:%d:%d:%d:%d:", 
    cfg->mac,
    temp,
    cfg->unitId,
		"0.0",
    cfg->tempMax,
    cfg->tempMin,
    0,
    0,
    0,
    1
	);

  Serial.print("Sending trigger: ");
  Serial.println(data);

  return mqttClient.publish("txdev/cr/t1", data);
}

void mqtt_callback(const char *topic, byte *payload, unsigned int length) {
  Serial.print("Message arrived [");
  Serial.print(topic);
  Serial.print("] ");
  for (int i=0;i<length;i++) {
    Serial.print((char)payload[i]);
  }
  Serial.println();

  char* payloadStr = (char*)malloc(length + 1);
  memcpy(payloadStr, payload, length);
  payloadStr[length] = '\0';

  config_parse_data(payloadStr);

  free(payloadStr);

  mqtt_publish_config();
};

void setup()
{
  Serial.begin(115200);
  delay(2000);

  pinMode(PIN_LCD_BL, ANALOG);
  analogWrite(PIN_LCD_BL, 128);
  
  bool result = _gfx.init();
  if (result) {
      _gfx.setRotation(0);
      _gfx.fillScreen(TFT_BLACK);
  }

  // TODO move to config file
  const char *ssid = "Beeblebrox";
  const char *password = "618W!t,.";

  if (ssid != NULL && password != NULL)
  {
    wifi = new WifiHandler();
    wifi->setup(ssid, password);
  }

  mqttClient.setServer("api.txdevsystems.co.za", 1883);
  mqttClient.setCallback(mqtt_callback);

  sensors.begin();

  if (!mqttClient.connected()) {
    reconnect();
  }
  mqttClient.loop();

  if (!mqtt_publish_config()) {
    Serial.println("Failed to publish config");
  }
}

void loop()
{
  if (wifi != nullptr) {
    wifi->loop();
  }

  if (!mqttClient.connected()) {
    reconnect();
  }

  mqttClient.loop();

  const Config *cfg = config_get();
  unsigned long currentTime = millis();

  if (lastTempData == 0 || currentTime - lastTempData >= (15 * 1000)) {
    sensors.requestTemperatures();
    temperatureC = sensors.getTempCByIndex(0);
    Serial.print("Temperature: ");
    Serial.print(temperatureC);
    Serial.println("°C");
    lastTempData = currentTime;
  }

  if (temperatureC < cfg->tempMin || temperatureC > cfg->tempMax) {
    if (lastTrigger == 0 || currentTime - lastTrigger >= (cfg->triggerResendMinute * 60 * 1000)) {
      if (!mqtt_publish_trigger(temperatureC)) {
        Serial.println("MQTT trigger failed");
      }
      lastTrigger = currentTime;

      // force data publish
      lastDataPublish = 0;
    }
  }
  else
  {
    lastTrigger = 0;
  }

  if (lastDataPublish == 0 || currentTime - lastDataPublish >= (cfg->dataResendMinute * 60 * 1000)) {
    if (!mqtt_publish_data(temperatureC)) {
      Serial.println("MQTT data failed");
    } else {
      lastDataPublish = currentTime;
    }
  }

  delay(200);
}
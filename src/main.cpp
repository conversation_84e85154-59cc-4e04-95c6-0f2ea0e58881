#include <Arduino.h>
#include <LovyanGFX.hpp>
#include <WiFi.h>
#include <OneWire.h>
#include <DallasTemperature.h>
#include <WiFiClient.h>
#include <PubSubClient.h>

#include "lilygo-t-display-s3.h"
#include "WifiHandler.h"
#include "config.h"

#define ONE_WIRE_BUS 2
OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature sensors(&oneWire);

WiFiClient wifiClient;
PubSubClient mqttClient(wifiClient);

WifiHandler *wifi;
LGFX _gfx;

#define RGB565(r, g, b) ((((r) & 0xF8) << 8) | (((g) & 0xFC) << 3) | ((b) >> 3))

void reconnect()
{
  while (!mqttClient.connected()) {
    Serial.print("Attempting MQTT connection...");
    if (mqttClient.connect("ESP32Client")) {
      Serial.println("connected");
    } else {
      Serial.print("failed, rc=");
      Serial.print(mqttClient.state());
      Serial.println(" try again in 5 seconds");
      delay(5000);
    }
  }
}

bool mqtt_publish_config()
{
  const char *config = config_get_string();

  Serial.print("Sending config: ");
  Serial.println(config);

  return mqttClient.publish("txdev/cr/c1", config);
}

bool mqtt_publish_data(const float temp)
{
	char data[128];
  const Config *cfg = config_get();

	snprintf(data, sizeof(data),"%s:%.01f:%.01f:%.01f:%d:%d:%s:%d:%s:%s:%s:", 
    cfg->mac,
    cfg->tempMax,
    temp,
    cfg->tempMin,
    0,
    0,
		"12.0",
    0,
    "8.0",
		"",
		WiFi.RSSI()
	);

  Serial.print("Sending data: ");
  Serial.println(data);

  return mqttClient.publish("txdev/cr/d1", data);
}

void mqtt_callback(const char *topic, byte *payload, unsigned int length) {
  Serial.print("Message arrived [");
  Serial.print(topic);
  Serial.print("] ");
  for (int i=0;i<length;i++) {
    Serial.print((char)payload[i]);
  }
  Serial.println();
  config_parse_data(payload);
};

void setup()
{
  Serial.begin(115200);
  delay(2000);

  pinMode(PIN_LCD_BL, ANALOG);
  analogWrite(PIN_LCD_BL, 128);
  
  bool result = _gfx.init();
  if (result) {
      _gfx.setRotation(0);
      _gfx.fillScreen(TFT_BLACK);
  }

  _gfx.setCursor(10, 10);
  _gfx.setTextColor(RGB565(255, 255, 255));
  _gfx.print("ok");

  const char *ssid = "Beeblebrox";
  const char *password = "618W!t,.";

  if (ssid != NULL && password != NULL)
  {
    wifi = new WifiHandler();
    wifi->setup(ssid, password);
  }

  mqttClient.setServer("api.txdevsystems.co.za", 1883);
  mqttClient.setCallback(mqtt_callback);

  sensors.begin();

  if (!mqttClient.connected()) {
    reconnect();
  }
  mqttClient.loop();

  if (!mqtt_publish_config()) {
    Serial.println("Failed to publish config");
  }

  String cfgTopic = "C" + String(config_get()->mac);
  if (mqttClient.subscribe(cfgTopic.c_str())) {
    Serial.printf("Subscribed to config topic %s\n", cfgTopic.c_str());
  }
}

void loop()
{
  if (wifi != nullptr) {
    wifi->loop();
  }

  if (!mqttClient.connected()) {
    reconnect();
  }

  mqttClient.loop();

  // Only publish data every config->dataResendMinute minutes
  static unsigned long lastDataPublish = 0;
  const Config *cfg = config_get();
  unsigned long currentTime = millis();

  if (currentTime - lastDataPublish >= (cfg->dataResendMinute * 60 * 1000)) {
    sensors.requestTemperatures();
    float temperatureC = sensors.getTempCByIndex(0);
    Serial.print("Temperature: ");
    Serial.print(temperatureC);
    Serial.println("°C");

    if (!mqtt_publish_data(temperatureC)) {
      Serial.println("MQTT publish failed");
    } else {
      lastDataPublish = currentTime;
    }
  }

  delay(200);
}
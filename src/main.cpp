#include <Arduino.h>
#include <LovyanGFX.hpp>
#include <WiFi.h>
#include "WifiHandler.h"

WifiHandler *wifi;
LGFX& _gfx;

void setup()
{
  Serial.begin(115200);
  delay(2000);

   // Initialize backlight
    pinMode(PIN_LCD_BL, ANALOG);
    analogWrite(PIN_LCD_BL, 128);
    
    // Initialize display
    bool result = _gfx.init();
    if (result) {
        _gfx.setRotation(0);
        _gfx.fillScreen(TFT_BLACK); // Clear screen to black
    }

  const char *ssid = "Beeblebrox";
  const char *password = "618W!t,;";

  if (ssid != NULL && password != NULL)
  {
    wifi = new WifiHandler();
    wifi->setup(ssid, password);
  }
}

void loop()
{
  if (wifi != nullptr) {
    wifi->loop();
  }
  delay(100);
}
#include <Arduino.h>
#include <LovyanGFX.hpp>
#include <WiFi.h>
#include <OneWire.h>
#include <DallasTemperature.h>

#include "lilygo-t-display-s3.h"
#include "WifiHandler.h"

// Data wire is connected to GPIO4 (D2 on many boards)
#define ONE_WIRE_BUS 4

// Setup a oneWire instance to communicate with any OneWire devices
OneWire oneWire(ONE_WIRE_BUS);

// Pass our oneWire reference to Dallas Temperature sensor library
DallasTemperature sensors(&oneWire);

WifiHandler *wifi;
LGFX _gfx;

#define RGB565(r, g, b) ((((r) & 0xF8) << 8) | (((g) & 0xFC) << 3) | ((b) >> 3))

void setup()
{
  Serial.begin(115200);
  delay(2000);

  pinMode(PIN_LCD_BL, ANALOG);
  analogWrite(PIN_LCD_BL, 128);
  
  bool result = _gfx.init();
  if (result) {
      _gfx.setRotation(0);
      _gfx.fillScreen(TFT_BLACK);
  }

  _gfx.setCursor(10, 10);
  _gfx.setTextColor(RGB565(255, 255, 255));
  _gfx.print("ok");

  const char *ssid = "Beeblebrox";
  const char *password = "618W!t,.";

  if (ssid != NULL && password != NULL)
  {
    wifi = new WifiHandler();
    wifi->setup(ssid, password);
  }

  sensors.begin();
}

void loop()
{
  if (wifi != nullptr) {
    wifi->loop();
  }
  // Request temperature readings from the sensor
  sensors.requestTemperatures();
  
  // Fetch and print the temperature
  float temperatureC = sensors.getTempCByIndex(0);
  Serial.print("Temperature: ");
  Serial.print(temperatureC);
  Serial.println("°C");

  delay(2000);
}
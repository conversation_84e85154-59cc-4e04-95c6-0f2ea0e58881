#include <stdlib.h>

// #include "mqtt.h"

#include "config.h"

// Default startup config
static Config s_config = {
			.mac = "000000000000",
			.unitId = "NOTSET",
			.revision = VERS,
			.dataResendMinute = 20,
			.triggerResendMinute = 5,
			.tempMin = -4.0,
			.tempMax = 4.0,
		};

inline const Config *config_get(void)
{
	return &s_config;
}

const char *config_get_string(void)
{
	static char config_string[128];
	snprintf(config_string, sizeof(config_string), "%s:%s:%s:%s:%d:%d:%d:%.1f:%.1f:%d:",
		s_config.mac,
		s_config.mac,
		s_config.unitId,
		s_config.revision,
		0,
		s_config.dataResendMinute,
		0,
		config_get_temp_max(),
		config_get_temp_min(),
		s_config.triggerResendMinute);
	return config_string;
}

inline const char *config_get_mac(void)
{
	return s_config.mac;
}

void config_set_mac(const char *imei)
{
	strncpy(s_config.mac, imei, CONFIG_MAC_SIZE);
	s_config.mac[CONFIG_MAC_SIZE] = '\0';
}

inline const char *config_get_unit_id(void)
{
	return s_config.unitId;
}

inline const char *config_get_revision(void)
{
	return s_config.revision;
}

inline uint8_t config_get_trigger_minutes(void)
{
	return s_config.triggerResendMinute;
}

inline uint8_t config_get_data_minutes(void)
{
	return s_config.dataResendMinute;
}	

void config_parse_data(char *data)
{
	char *token = strtok(data, ":");
	int index = 0;
	while (token != NULL)
	{
		switch (index)
		{
		case 0: // Skip first :
			break;
		case 1:
			s_config.tempMax = atof(token);
			console_writeline("Max Temp: %s %d", token, s_config.tempMax);
			break;
		case 2:
			s_config.tempMin = atof(token);
			console_writeline("Min Temp: %s %d", token, s_config.tempMin);
			break;
		case 3:
			// Ignore door polarity
			break;
		case 4:
			s_config.dataResendMinute = atoi(token);
			break;
		case 5:
			// Ignore door alarm minutes
			break;
		case 6:
			// Ignore request location
			break;
		case 7:
			strncpy(s_config.unitId, token, sizeof(s_config.unitId) - 1);
			s_config.unitId[sizeof(s_config.unitId) - 1] = '\0';
			break;
		case 8:
			s_config.triggerResendMinute = atoi(token);
			break;
		}
		index++;
		token = strtok(NULL, ":");
	}
}

/*
  :20:12:1:20:5:0:Old_Set:5;
*/
bool config_from_message(char *data)
{
	int length = 0;

	char *tokenString = strdup(data);
	if (!tokenString)
	{
		console_writeline("Out of memory");
		return false;
	}

	int index = 0;
	char *token = strtok(tokenString, ",");
	while (token != NULL)
	{
		switch (index)
		{
		case 0:
			// Ignore QoS
			break;
		case 1:
			// Check IMEI (skip the leading "C and trailing ")
			token[CONFIG_MAC_SIZE + 1] = '\0';
			if (strcmp(&token[2], s_config.mac) != 0)
			{
				console_writeline("Not my config message");
				free(tokenString);
				return false;
			}
			break;
		case 2:
			length = atoi(token);
			break;
		case 3:
			// Sanity check, config string should end in ;
			if (token[length] != ';')
			{
				console_writeline("Not a valid config message");
				free(tokenString);
				return false;
			}
			else
			{
				config_parse_data(token);
			}
			break;
		}
		index++;
		token = strtok(NULL, ",");
	}
	free(tokenString);
	return true;
}

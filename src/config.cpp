#include "config.h"

// Default startup config
static Config s_config = {
			"000000000000",  // mac
			"Notset",        // unitId
			VERS,            // revision
			20,              // dataResendMinute
			5,               // triggerResendMinute
			-4.0,            // tempMin
			4.0              // tempMax
		};

const Config *config_get(void)
{
	return &s_config;
}

const char *config_get_string(void)
{
	static char config_string[128];
	snprintf(config_string, sizeof(config_string), "%s:%s:%s:%s:%d:%d:%d:%.1f:%.1f:%d:",
		s_config.mac,
		s_config.mac,
		s_config.unitId,
		s_config.revision,
		0,
		s_config.dataResendMinute,
		0,
		s_config.tempMax,
		s_config.tempMin,
		s_config.triggerResendMinute);
	return config_string;
}

inline const char *config_get_mac(void)
{
	return s_config.mac;
}

void config_set_mac(const char *imei)
{
	strncpy(s_config.mac, imei, CONFIG_MAC_SIZE);
	s_config.mac[CONFIG_MAC_SIZE] = '\0';
}

inline const char *config_get_unit_id(void)
{
	return s_config.unitId;
}

inline const char *config_get_revision(void)
{
	return s_config.revision;
}

inline uint8_t config_get_trigger_minutes(void)
{
	return s_config.triggerResendMinute;
}

inline uint8_t config_get_data_minutes(void)
{
	return s_config.dataResendMinute;
}	

void config_parse_data(char *data)
{
	Serial.printf("Parse config : %s\n", data);

	char *token = strtok(data, ":");
	int index = 0;
	while (token != NULL)
	{
		switch (index)
		{
		case 0: // Skip first :
			break;
		case 1:
			s_config.tempMax = atof(token);
			Serial.printf("Max Temp: %s %d\n", token, s_config.tempMax);
			break;
		case 2:
			s_config.tempMin = atof(token);
			Serial.printf("Min Temp: %s %d\n", token, s_config.tempMin);
			break;
		case 3:
			// Ignore door polarity
			break;
		case 4:
			s_config.dataResendMinute = atoi(token);
			break;
		case 5:
			// Ignore door alarm minutes
			break;
		case 6:
			// Ignore request location
			break;
		case 7:
			strncpy(s_config.unitId, token, sizeof(s_config.unitId) - 1);
			s_config.unitId[sizeof(s_config.unitId) - 1] = '\0';
			break;
		case 8:
			s_config.triggerResendMinute = atoi(token);
			break;
		}
		index++;
		token = strtok(NULL, ":");
	}
}
#include "config.h"

// Default startup config
static Config s_config = {
			"000000000000",  // mac
			"NOCFG",         // unitId
			VERS,            // revision
			20,              // dataResendMinute
			5,               // triggerResendMinute
			0,               // doorAlarmMinute
			0,               // doorPolarity
			-40.0,           // tempMin
			40.0             // tempMax
		};

const Config *config_get(void)
{
	return &s_config;
}

const char *config_get_string(void)
{
	static char config_string[128];
	snprintf(config_string, sizeof(config_string), "%s:%s:%s:%s:%d:%d:%d:%.1f:%.1f:%d:",
		s_config.mac,
		s_config.mac,
		s_config.unitId,
		s_config.revision,
		s_config.doorPolarity,
		s_config.dataResendMinute,
		s_config.doorAlarmMinute,
		s_config.tempMax,
		s_config.tempMin,
		s_config.triggerResendMinute);
	return config_string;
}

inline const char *config_get_mac(void)
{
	return s_config.mac;
}

void config_set_mac(const char *imei)
{
	strncpy(s_config.mac, imei, CONFIG_MAC_SIZE);
	s_config.mac[CONFIG_MAC_SIZE] = '\0';
}

inline const char *config_get_unit_id(void)
{
	return s_config.unitId;
}

inline const char *config_get_revision(void)
{
	return s_config.revision;
}

inline uint8_t config_get_trigger_minutes(void)
{
	return s_config.triggerResendMinute;
}

inline uint8_t config_get_data_minutes(void)
{
	return s_config.dataResendMinute;
}	

void config_parse_data(char *data)
{
	Serial.printf("Parse config: %s\n", data);

	char *token = strtok(data, ":");
	int index = 0;
	while (token != NULL)
	{
		switch (index)
		{
		case 0:
			s_config.tempMax = atof(token);
			Serial.printf("Max Temp: %.1f\n", s_config.tempMax);
			break;
		case 1:
			s_config.tempMin = atof(token);
			Serial.printf("Min Temp: %.1f\n", s_config.tempMin);
			break;
		case 2:
			s_config.doorPolarity = atoi(token);
			Serial.printf("Door Polarity: %d\n", s_config.doorPolarity);
			break;
		case 3:
			s_config.dataResendMinute = atoi(token);
			Serial.printf("Data Resend minutes: %d\n", s_config.dataResendMinute);
			break;
		case 4:
			s_config.doorAlarmMinute = atoi(token);
			Serial.printf("Door Alarm minutes: %d\n", s_config.doorAlarmMinute);
			break;
		case 5:
			Serial.printf("Ignore request location %s\n", token);
			break;
		case 6:
			strncpy(s_config.unitId, token, sizeof(s_config.unitId) - 1);
			s_config.unitId[sizeof(s_config.unitId) - 1] = '\0';
			Serial.printf("Unit ID: %s\n", s_config.unitId);
			break;
		case 7:
			s_config.triggerResendMinute = atoi(token);
			Serial.printf("Trigger Resend minutes: %d\n", s_config.triggerResendMinute);
			break;
		}
		index++;
		token = strtok(NULL, ":");
	}
}
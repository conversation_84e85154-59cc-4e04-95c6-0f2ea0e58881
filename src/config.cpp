#include "config.h"
#include <LittleFS.h>
#include <ArduinoJson.h>

// Default startup config
static Config s_config = {
			"000000000000",  // mac
			"NOCFG",         // unitId
			VERS,            // revision
			20,              // dataResendMinute
			5,               // triggerResendMinute
			0,               // doorAlarmMinute
			0,               // doorPolarity
			-40.0,           // tempMin
			40.0             // tempMax
		};

// Default WiFi config
static WifiConfig s_wifi_config = {
			"Beeblebrox",    // ssid
			"618W!t,."       // password
		};

const Config *config_get(void)
{
	return &s_config;
}

const char *config_get_string(void)
{
	static char config_string[128];
	snprintf(config_string, sizeof(config_string), "%s:%s:%s:%s:%d:%d:%d:%.1f:%.1f:%d:",
		s_config.mac,
		s_config.mac,
		s_config.unitId,
		s_config.revision,
		s_config.doorPolarity,
		s_config.dataResendMinute,
		s_config.doorAlarmMinute,
		s_config.tempMax,
		s_config.tempMin,
		s_config.triggerResendMinute);
	return config_string;
}

inline const char *config_get_mac(void)
{
	return s_config.mac;
}

void config_set_mac(const char *imei)
{
	strncpy(s_config.mac, imei, CONFIG_MAC_SIZE);
	s_config.mac[CONFIG_MAC_SIZE] = '\0';
}

inline const char *config_get_unit_id(void)
{
	return s_config.unitId;
}

inline const char *config_get_revision(void)
{
	return s_config.revision;
}

inline uint8_t config_get_trigger_minutes(void)
{
	return s_config.triggerResendMinute;
}

inline uint8_t config_get_data_minutes(void)
{
	return s_config.dataResendMinute;
}

const WifiConfig *config_get_wifi(void)
{
	return &s_wifi_config;
}

void config_set_wifi(const char *ssid, const char *password)
{
	strncpy(s_wifi_config.ssid, ssid, CONFIG_WIFI_SSID_SIZE);
	s_wifi_config.ssid[CONFIG_WIFI_SSID_SIZE] = '\0';
	strncpy(s_wifi_config.password, password, CONFIG_WIFI_PASSWORD_SIZE);
	s_wifi_config.password[CONFIG_WIFI_PASSWORD_SIZE] = '\0';
}

void config_parse_data(char *data)
{
	Serial.printf("Parse config: %s\n", data);

	char *token = strtok(data, ":");
	int index = 0;
	while (token != NULL)
	{
		switch (index)
		{
		case 0:
			s_config.tempMax = atof(token);
			Serial.printf("Max Temp: %.1f\n", s_config.tempMax);
			break;
		case 1:
			s_config.tempMin = atof(token);
			Serial.printf("Min Temp: %.1f\n", s_config.tempMin);
			break;
		case 2:
			s_config.doorPolarity = atoi(token);
			Serial.printf("Door Polarity: %d\n", s_config.doorPolarity);
			break;
		case 3:
			s_config.dataResendMinute = atoi(token);
			Serial.printf("Data Resend minutes: %d\n", s_config.dataResendMinute);
			break;
		case 4:
			s_config.doorAlarmMinute = atoi(token);
			Serial.printf("Door Alarm minutes: %d\n", s_config.doorAlarmMinute);
			break;
		case 5:
			Serial.printf("Ignore request location %s\n", token);
			break;
		case 6:
			strncpy(s_config.unitId, token, sizeof(s_config.unitId) - 1);
			s_config.unitId[sizeof(s_config.unitId) - 1] = '\0';
			Serial.printf("Unit ID: %s\n", s_config.unitId);
			break;
		case 7:
			s_config.triggerResendMinute = atoi(token);
			Serial.printf("Trigger Resend minutes: %d\n", s_config.triggerResendMinute);
			break;
		}
		index++;
		token = strtok(NULL, ":");
	}

	// Save updated config to file after parsing
	config_save_to_file();
}

bool config_init_filesystem() {
	if (!LittleFS.begin()) {
		Serial.println("Failed to mount LittleFS");
		return false;
	}
	Serial.println("LittleFS mounted successfully");
	return true;
}

bool config_load_from_file() {
	if (!LittleFS.exists("/config.json")) {
		Serial.println("Config file doesn't exist, using defaults");
		return false;
	}

	File file = LittleFS.open("/config.json", "r");
	if (!file) {
		Serial.println("Failed to open config file");
		return false;
	}

	JsonDocument doc;
	DeserializationError error = deserializeJson(doc, file);
	file.close();

	if (error) {
		Serial.printf("Failed to parse config file: %s\n", error.c_str());
		return false;
	}

	// Load config values from JSON
	if (doc["mac"].is<const char*>()) {
		strncpy(s_config.mac, doc["mac"], CONFIG_MAC_SIZE);
		s_config.mac[CONFIG_MAC_SIZE] = '\0';
	}
	if (doc["unitId"].is<const char*>()) {
		strncpy(s_config.unitId, doc["unitId"], CONFIG_UNITID_SIZE);
		s_config.unitId[CONFIG_UNITID_SIZE] = '\0';
	}
	if (doc["dataResendMinute"].is<unsigned char>()) {
		s_config.dataResendMinute = doc["dataResendMinute"];
	}
	if (doc["triggerResendMinute"].is<unsigned char>()) {
		s_config.triggerResendMinute = doc["triggerResendMinute"];
	}
	if (doc["doorAlarmMinute"].is<unsigned char>()) {
		s_config.doorAlarmMinute = doc["doorAlarmMinute"];
	}
	if (doc["doorPolarity"].is<unsigned char>()) {
		s_config.doorPolarity = doc["doorPolarity"];
	}
	if (doc["tempMin"].is<float>()) {
		s_config.tempMin = doc["tempMin"];
	}
	if (doc["tempMax"].is<float>()) {
		s_config.tempMax = doc["tempMax"];
	}

	// Load WiFi config
	if (doc["wifi"].is<JsonObject>()) {
		JsonObject wifi = doc["wifi"];
		if (wifi["ssid"].is<const char*>()) {
			strncpy(s_wifi_config.ssid, wifi["ssid"], CONFIG_WIFI_SSID_SIZE);
			s_wifi_config.ssid[CONFIG_WIFI_SSID_SIZE] = '\0';
		}
		if (wifi["password"].is<const char*>()) {
			strncpy(s_wifi_config.password, wifi["password"], CONFIG_WIFI_PASSWORD_SIZE);
			s_wifi_config.password[CONFIG_WIFI_PASSWORD_SIZE] = '\0';
		}
	}

	Serial.println("Config loaded from file successfully");
	return true;
}

bool config_save_to_file() {
	JsonDocument doc;

	// Create JSON object with current config
	doc["mac"] = s_config.mac;
	doc["unitId"] = s_config.unitId;
	doc["revision"] = s_config.revision;
	doc["dataResendMinute"] = s_config.dataResendMinute;
	doc["triggerResendMinute"] = s_config.triggerResendMinute;
	doc["doorAlarmMinute"] = s_config.doorAlarmMinute;
	doc["doorPolarity"] = s_config.doorPolarity;
	doc["tempMin"] = s_config.tempMin;
	doc["tempMax"] = s_config.tempMax;

	// Add WiFi config
	JsonObject wifi = doc["wifi"].to<JsonObject>();
	wifi["ssid"] = s_wifi_config.ssid;
	wifi["password"] = s_wifi_config.password;

	File file = LittleFS.open("/config.json", "w");
	if (!file) {
		Serial.println("Failed to open config file for writing");
		return false;
	}

	if (serializeJson(doc, file) == 0) {
		Serial.println("Failed to write config to file");
		file.close();
		return false;
	}

	file.close();
	Serial.println("Config saved to file successfully");
	return true;
}
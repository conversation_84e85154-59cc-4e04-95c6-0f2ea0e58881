#ifndef WIFI_HANDLER_H
#define WIFI_HANDLER_H

#include <WiFi.h>
#include <Arduino.h>

class WifiHandler {
private:
    const char* ssid;
    const char* password;
    bool isConnected;
    unsigned long lastConnectionAttempt;
    static const unsigned long CONNECTION_TIMEOUT = 10000; // 10 seconds
    static const unsigned long RETRY_INTERVAL = 30000;     // 30 seconds

public:
    WifiHandler();
    ~WifiHandler();
    
    void setup(const char* ssid, const char* password);
    void loop();
    bool connect();
    void disconnect();
    bool isWifiConnected();
    String getLocalIP();
    int getSignalStrength();
    void printConnectionInfo();
    
private:
    void onWiFiEvent(WiFiEvent_t event);
    static void WiFiEventHandler(WiFiEvent_t event);
};

#endif // WIFI_HANDLER_H

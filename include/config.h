#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>

#define CONFIG_MAC_SIZE 12
#define CONFIG_UNITID_SIZE 8
#define CONFIG_REVISION_SIZE 4
#define CONFIG_WIFI_SSID_SIZE 32
#define CONFIG_WIFI_PASSWORD_SIZE 64

#define VERS "4.31"

typedef struct
{
    char mac[CONFIG_MAC_SIZE + 1];
    char unitId[CONFIG_UNITID_SIZE + 1];
    char revision[CONFIG_REVISION_SIZE + 1];
    unsigned char dataResendMinute;
    unsigned char triggerResendMinute;
    unsigned char doorAlarmMinute;
    unsigned char doorPolarity;
    float tempMin;
    float tempMax;
} Config;

typedef struct {
    char ssid[CONFIG_WIFI_SSID_SIZE + 1];
    char password[CONFIG_WIFI_PASSWORD_SIZE + 1];
} WifiConfig;

const Config *config_get(void);
const WifiConfig *config_get_wifi(void);
void config_set_wifi(const char *ssid, const char *password);
const char *config_get_mac(void);
void config_set_mac(const char *mac);
const char *config_get_unit_id(void);
const char *config_get_revision(void);
const char *config_get_string(void);
unsigned char config_get_trigger_minutes(void);
unsigned char config_get_data_minutes(void);
void config_parse_data(char *data);
bool config_init_filesystem(void);
bool config_load_from_file(void);
bool config_save_to_file(void);

#endif // CONFIG_H
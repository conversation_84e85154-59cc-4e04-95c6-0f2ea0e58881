#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>

#define CONFIG_MAC_SIZE 12
#define CONFIG_UNITID_SIZE 8
#define CONFIG_REVISION_SIZE 4

#define VERS "4.3R"

typedef struct
{
    char mac[CONFIG_MAC_SIZE + 1];
    char unitId[CONFIG_UNITID_SIZE + 1];
    char revision[CONFIG_REVISION_SIZE + 1];
    unsigned char dataResendMinute;
    unsigned char triggerResendMinute;
    float tempMin;
    float tempMax;
} Config;

const Config *config_get(void);
const char *config_get_mac(void);
void config_set_mac(const char *mac);
const char *config_get_iccid(void);
void config_set_iccid(const char *iccid);
const char *config_get_unit_id(void);
const char *config_get_revision(void);
const char *config_get_string(void);
unsigned char config_get_trigger_minutes(void);
unsigned char config_get_data_minutes(void);
unsigned char config_get_door_alarm_minutes(void);
void config_parse_data(char *data);

#endif // CONFIG_H